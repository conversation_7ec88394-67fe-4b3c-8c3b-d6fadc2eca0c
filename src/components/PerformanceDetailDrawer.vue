<template>
  <el-drawer
    v-model="visible"
    :title="null"
    direction="rtl"
    size="50%"
    class="performance-detail-drawer"
    :before-close="handleClose"
  >
    <!-- 简化的顶部区域 -->
    <div v-if="performanceData" class="perf-header">
      <!-- 主要信息行 -->
      <div class="header-main">
        <div class="kol-info">
          <el-icon class="kol-icon"><User /></el-icon>
          <span class="kol-label">KOL ID:</span>
          <span class="kol-value">{{ performanceData.kol_id }}</span>
        </div>

        <div class="header-actions">
          <el-link
            v-if="performanceData.post_link"
            :href="performanceData.post_link"
            target="_blank"
            type="primary"
            :underline="false"
            class="view-post-link"
          >
            <el-icon><Link /></el-icon>
            查看原帖
          </el-link>
          <el-button type="primary" @click="handleEdit" class="edit-button">
            <el-icon><Edit /></el-icon>
            编辑
          </el-button>
        </div>
      </div>

      <!-- 详细信息行 -->
      <div class="info-grid">
        <div v-if="performanceData.project_code" class="info-item">
          <el-icon><CollectionTag /></el-icon>
          <span class="info-label">项目编码</span>
          <span class="info-value">{{ performanceData.project_code }}</span>
        </div>

        <div class="info-item">
          <el-icon>
            <VideoPlay v-if="performanceData.platform === 'tiktok'" />
            <Picture v-else-if="performanceData.platform === 'instagram'" />
            <Monitor v-else-if="performanceData.platform === 'youtube'" />
          </el-icon>
          <span class="info-label">平台</span>
          <span class="info-value">{{ getPlatformLabel(performanceData.platform) }}</span>
        </div>

        <div v-if="performanceData.post_date" class="info-item">
          <el-icon><Calendar /></el-icon>
          <span class="info-label">发布时间</span>
          <span class="info-value">{{ formatDate(performanceData.post_date) }}</span>
        </div>
      </div>
    </div>

    <div v-if="performanceData" class="content-scroll-area">
      <!-- 核心数据摘要卡片 -->
      <el-card class="summary-card" shadow="hover">
        <div class="summary-grid">
          <div v-for="item in summaryList" :key="item.key" class="summary-item">
            <el-icon :size="28" :style="{ color: item.color }">
              <component :is="item.icon" />
            </el-icon>
            <div class="summary-value">{{ item.value }}</div>
            <div class="summary-label">{{ item.label }}</div>
          </div>
        </div>
      </el-card>

      <!-- 分阶段数据卡片 -->
      <el-card class="stage-card" shadow="hover">
        <template #header>
          <h3>分阶段数据</h3>
        </template>
        <div class="stage-grid">
          <div v-for="stage in stageList" :key="stage.key" class="stage-item">
            <div class="stage-title">{{ stage.label }}</div>
            <div class="stage-metrics">
              <div v-for="metric in stage.metrics" :key="metric.key" class="stage-metric">
                <el-icon :size="18" :style="{ color: metric.color }">
                  <component :is="metric.icon" />
                </el-icon>
                <span class="metric-value">{{ metric.value }}</span>
                <span class="metric-label">{{ metric.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 趋势折线图卡片 -->
      <el-card class="trend-card" shadow="hover">
        <template #header>
          <div style="display: flex; justify-content: space-between; align-items: center">
            <h3>数据趋势</h3>
            <el-checkbox-group v-model="selectedSeries" size="small" @change="handleSeriesChange">
              <el-checkbox
                v-for="s in allSeries"
                :key="s.key"
                :label="s.key"
                :disabled="selectedSeries.length === 1 && selectedSeries[0] === s.key"
                :style="{ color: s.color, marginRight: '8px' }"
                >{{ s.label }}</el-checkbox
              >
            </el-checkbox-group>
          </div>
        </template>
        <v-chart :option="trendChartOption" autoresize style="height: 320px; width: 100%" />
      </el-card>

      <!-- 支付信息卡片 -->
      <el-card v-if="performanceData.payment" class="payment-card" shadow="hover">
        <template #header>
          <div style="display: flex; justify-content: space-between; align-items: center">
            <h3>
              <el-icon style="margin-right: 8px; color: #67c23a"><Money /></el-icon>
              支付信息
            </h3>
          </div>
        </template>

        <el-descriptions :column="2" size="large" border class="payment-descriptions">
          <el-descriptions-item label="支付金额">
            <span class="payment-amount">
              ${{ formatAmount(performanceData.payment.payment_amount) }}
            </span>
          </el-descriptions-item>

          <el-descriptions-item label="支付日期">
            {{ formatDate(performanceData.payment.payout_date) }}
          </el-descriptions-item>

          <el-descriptions-item label="PayPal账户">
            {{ performanceData.payment.paypal_accounts || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="跟进人">
            {{ performanceData.payment.tracker || '-' }}
          </el-descriptions-item>

          <el-descriptions-item label="资金来源">
            <el-tag :type="getFundSourceTagType(performanceData.payment.fund_source)">
              {{ getFundSourceLabel(performanceData.payment.fund_source) }}
            </el-tag>
          </el-descriptions-item>

          <el-descriptions-item
            label="支付截图"
            v-if="performanceData.payment.payment_screenshot_filename"
          >
            <el-button type="primary" size="small" @click="viewPaymentScreenshot">
              <el-icon style="margin-right: 4px"><Picture /></el-icon>
              查看截图
            </el-button>
          </el-descriptions-item>

          <el-descriptions-item label="备注" :span="2" v-if="performanceData.payment.note">
            {{ performanceData.payment.note }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 详细信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <h3>其他信息</h3>
        </template>
        <el-descriptions
          :column="2"
          border
          size="large"
          :label-style="{ width: '120px', textAlign: 'right' }"
        >
          <el-descriptions-item label="创建时间">
            {{ formatDate(performanceData.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后更新">
            {{ formatDate(performanceData.updated_at) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>

    <!-- 编辑表单弹窗 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑绩效信息"
      width="900px"
      :before-close="handleEditCancel"
      class="edit-dialog"
      append-to-body
    >
      <div class="edit-form-container" v-loading="editLoading">
        <el-form
          ref="editFormRef"
          :model="editForm"
          :rules="editRules"
          label-width="120px"
          class="edit-form"
        >
          <!-- 基本信息 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Monitor /></el-icon>
                基本信息
              </h4>
            </div>
            <div class="section-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="平台" prop="platform">
                    <el-select
                      v-model="editForm.platform"
                      placeholder="请选择平台"
                      style="width: 100%"
                      size="large"
                    >
                      <el-option label="TikTok" value="tiktok">
                        <div class="platform-option">
                          <el-icon color="#ff0050"><VideoPlay /></el-icon>
                          <span>TikTok</span>
                        </div>
                      </el-option>
                      <el-option label="Instagram" value="instagram">
                        <div class="platform-option">
                          <el-icon color="#e4405f"><Picture /></el-icon>
                          <span>Instagram</span>
                        </div>
                      </el-option>
                      <el-option label="YouTube" value="youtube">
                        <div class="platform-option">
                          <el-icon color="#ff0000"><Monitor /></el-icon>
                          <span>YouTube</span>
                        </div>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="KOL ID" prop="kol_id">
                    <el-input v-model="editForm.kol_id" placeholder="请输入KOL ID" size="large" />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="项目编码" prop="project_code">
                    <el-input
                      v-model="editForm.project_code"
                      placeholder="请输入项目编码"
                      size="large"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="绩效状态" prop="status">
                    <el-select
                      v-model="editForm.status"
                      placeholder="请选择绩效状态"
                      style="width: 100%"
                      size="large"
                    >
                      <el-option label="待评估" value="pending" />
                      <el-option label="表现差" value="poor" />
                      <el-option label="表现中等" value="average" />
                      <el-option label="表现良好" value="good" />
                      <el-option label="表现优秀" value="excellent" />
                      <el-option label="数据收集失败" value="failed" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="每千次曝光费用" prop="cpm">
                    <el-input-number
                      v-model="editForm.cpm"
                      :min="0"
                      :precision="4"
                      style="width: 100%"
                      placeholder="请输入CPM"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发布日期" prop="post_date">
                    <el-date-picker
                      v-model="postDateValue"
                      type="datetime"
                      placeholder="请选择发布日期"
                      style="width: 100%"
                      size="large"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="帖子链接" prop="post_link">
                <el-input v-model="editForm.post_link" placeholder="请输入帖子链接" size="large" />
              </el-form-item>
            </div>
          </div>

          <!-- 总体数据指标 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><DataAnalysis /></el-icon>
                总体数据指标
              </h4>
            </div>
            <div class="section-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="总观看数" prop="views_total">
                    <el-input-number
                      v-model="editForm.views_total"
                      :min="0"
                      style="width: 100%"
                      placeholder="请输入总观看数"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="总点赞数" prop="likes_total">
                    <el-input-number
                      v-model="editForm.likes_total"
                      :min="0"
                      style="width: 100%"
                      placeholder="请输入总点赞数"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="总评论数" prop="comments_total">
                    <el-input-number
                      v-model="editForm.comments_total"
                      :min="0"
                      style="width: 100%"
                      placeholder="请输入总评论数"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="总分享数" prop="shares_total">
                    <el-input-number
                      v-model="editForm.shares_total"
                      :min="0"
                      style="width: 100%"
                      placeholder="请输入总分享数"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 第1天数据 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Calendar /></el-icon>
                第1天数据
              </h4>
            </div>
            <div class="section-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="观看数" prop="views_day1">
                    <el-input-number
                      v-model="editForm.views_day1"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="点赞数" prop="likes_day1">
                    <el-input-number
                      v-model="editForm.likes_day1"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="评论数" prop="comments_day1">
                    <el-input-number
                      v-model="editForm.comments_day1"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分享数" prop="shares_day1">
                    <el-input-number
                      v-model="editForm.shares_day1"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 第3天数据 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Calendar /></el-icon>
                第3天数据
              </h4>
            </div>
            <div class="section-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="观看数" prop="views_day3">
                    <el-input-number
                      v-model="editForm.views_day3"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="点赞数" prop="likes_day3">
                    <el-input-number
                      v-model="editForm.likes_day3"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="评论数" prop="comments_day3">
                    <el-input-number
                      v-model="editForm.comments_day3"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分享数" prop="shares_day3">
                    <el-input-number
                      v-model="editForm.shares_day3"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 第7天数据 -->
          <div class="form-section">
            <div class="section-header">
              <h4 class="section-title">
                <el-icon><Calendar /></el-icon>
                第7天数据
              </h4>
            </div>
            <div class="section-content">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="观看数" prop="views_day7">
                    <el-input-number
                      v-model="editForm.views_day7"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="点赞数" prop="likes_day7">
                    <el-input-number
                      v-model="editForm.likes_day7"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="评论数" prop="comments_day7">
                    <el-input-number
                      v-model="editForm.comments_day7"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="分享数" prop="shares_day7">
                    <el-input-number
                      v-model="editForm.shares_day7"
                      :min="0"
                      style="width: 100%"
                      size="large"
                      :controls="false"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleEditCancel" :disabled="editLoading">取消</el-button>
          <el-button type="primary" @click="handleEditSave" :loading="editLoading">
            保存修改
          </el-button>
        </div>
      </template>
    </el-dialog>
  </el-drawer>
</template>

<script setup lang="ts" name="PerformanceDetailDrawer">
import { computed, reactive, ref, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  Edit,
  VideoPlay,
  Picture,
  Monitor,
  ChatDotRound,
  View,
  DataAnalysis,
  Calendar,
  Star,
  Share,
  Link,
  Money,
  User,
  CollectionTag,
} from '@element-plus/icons-vue'
import { use } from 'echarts/core'
import VChart from 'vue-echarts'
import { LineChart } from 'echarts/charts'
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  TitleComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

use([LineChart, GridComponent, TooltipComponent, LegendComponent, TitleComponent, CanvasRenderer])

// 折线图序列定义和选择
const allSeries = [
  { key: 'views', label: '观看数', color: '#409eff' },
  { key: 'likes', label: '点赞数', color: '#f56c6c' },
  { key: 'comments', label: '评论数', color: '#67c23a' },
  { key: 'shares', label: '分享数', color: '#e6a23c' },
]
const selectedSeries = ref(allSeries.map((s) => s.key)) // 默认全选

// 保证至少有一个序列被选中
function handleSeriesChange(val: string[]) {
  if (val.length === 0) {
    // 阻止全部取消，回退为上一次选择
    selectedSeries.value = [allSeries[0].key]
  }
}

// 类型定义
// 支付信息接口（与Payment接口保持一致）
interface PaymentInfo {
  id: number
  performance_id: number
  payment_amount: string | null
  paypal_accounts: string | null
  tracker: string | null
  fund_source: string | null
  payment_screenshot_filename: string | null
  note: string | null
  payout_date: string | null
  created_at: string
  updated_at: string
}

interface PerformanceItem {
  id: number
  platform: 'tiktok' | 'instagram' | 'youtube'
  kol_id: string
  project_code?: string
  cpm?: number | null
  post_link: string
  post_date?: string | null
  views_total?: number | null
  likes_total?: number | null
  comments_total?: number | null
  shares_total?: number | null
  views_day1?: number | null
  likes_day1?: number | null
  comments_day1?: number | null
  shares_day1?: number | null
  views_day3?: number | null
  likes_day3?: number | null
  comments_day3?: number | null
  shares_day3?: number | null
  views_day7?: number | null
  likes_day7?: number | null
  comments_day7?: number | null
  shares_day7?: number | null
  status: 'pending' | 'poor' | 'average' | 'good' | 'excellent' | 'failed'
  created_at: string
  updated_at: string
  // 新增支付信息
  payment?: PaymentInfo | null
}

// Props
interface Props {
  modelValue: boolean
  performanceData?: PerformanceItem | null
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  performanceData: null,
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  edit: [performance: PerformanceItem]
  save: [performance: PerformanceItem]
}>()

// 响应式数据
const showEditDialog = ref(false)
const editLoading = ref(false)
const editFormRef = ref<FormInstance>()

// 编辑表单数据
const editForm = reactive<PerformanceItem>({
  id: 0,
  platform: 'tiktok',
  kol_id: '',
  project_code: '',
  cpm: null,
  post_link: '',
  post_date: null,
  views_total: null,
  likes_total: null,
  comments_total: null,
  shares_total: null,
  views_day1: null,
  likes_day1: null,
  comments_day1: null,
  shares_day1: null,
  views_day3: null,
  likes_day3: null,
  comments_day3: null,
  shares_day3: null,
  views_day7: null,
  likes_day7: null,
  comments_day7: null,
  shares_day7: null,
  status: 'pending',
  created_at: '',
  updated_at: '',
})

// 表单验证规则
const editRules: FormRules = {
  platform: [{ required: true, message: '请选择平台', trigger: 'change' }],
  kol_id: [{ required: true, message: '请输入KOL ID', trigger: 'blur' }],
  post_link: [{ required: true, message: '请输入帖子链接', trigger: 'blur' }],
  status: [{ required: true, message: '请选择绩效状态', trigger: 'change' }],
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const postDateValue = computed({
  get: () => (editForm.post_date ? new Date(editForm.post_date) : undefined),
  set: (val: Date | undefined) => {
    editForm.post_date = val ? val.toISOString() : null
  },
})

// 折线图 option
const trendChartOption = computed(() => {
  if (!props.performanceData) return {}
  const d = props.performanceData
  const xAxis = ['第1天', '第3天', '第7天', '总计']
  const seriesMap: Record<string, (number | null | undefined)[]> = {
    views: [d.views_day1, d.views_day3, d.views_day7, d.views_total],
    likes: [d.likes_day1, d.likes_day3, d.likes_day7, d.likes_total],
    comments: [d.comments_day1, d.comments_day3, d.comments_day7, d.comments_total],
    shares: [d.shares_day1, d.shares_day3, d.shares_day7, d.shares_total],
  }
  const showSeries = allSeries.filter((s) => selectedSeries.value.includes(s.key))
  return {
    tooltip: { trigger: 'axis' },
    legend: { data: showSeries.map((s) => s.label) },
    grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
    xAxis: { type: 'category', data: xAxis },
    yAxis: { type: 'value' },
    series: showSeries.map((s) => ({
      name: s.label,
      type: 'line',
      data: seriesMap[s.key],
      smooth: true,
      lineStyle: { color: s.color },
      itemStyle: { color: s.color },
    })),
    color: showSeries.map((s) => s.color),
  }
})

// 核心数据摘要
const summaryList = computed(() => {
  if (!props.performanceData) return []
  return [
    {
      key: 'cpm',
      label: 'CPM',
      value: props.performanceData.cpm ? `$${props.performanceData.cpm.toFixed(2)}` : '-',
      icon: DataAnalysis,
      color: '#409eff',
    },
    {
      key: 'views',
      label: '观看',
      value: formatNumber(props.performanceData.views_total),
      icon: View,
      color: '#409eff',
    },
    {
      key: 'likes',
      label: '点赞',
      value: formatNumber(props.performanceData.likes_total),
      icon: Star,
      color: '#f56c6c',
    },
    {
      key: 'comments',
      label: '评论',
      value: formatNumber(props.performanceData.comments_total),
      icon: ChatDotRound,
      color: '#67c23a',
    },
    {
      key: 'shares',
      label: '分享',
      value: formatNumber(props.performanceData.shares_total),
      icon: Share,
      color: '#e6a23c',
    },
  ]
})

// 分阶段数据
const stageList = computed(() => {
  if (!props.performanceData) return []
  return [
    {
      key: 'day1',
      label: '第1天',
      metrics: [
        {
          key: 'views',
          label: '观看',
          value: formatNumber(props.performanceData.views_day1),
          icon: View,
          color: '#409eff',
        },
        {
          key: 'likes',
          label: '点赞',
          value: formatNumber(props.performanceData.likes_day1),
          icon: Star,
          color: '#f56c6c',
        },
        {
          key: 'comments',
          label: '评论',
          value: formatNumber(props.performanceData.comments_day1),
          icon: ChatDotRound,
          color: '#67c23a',
        },
        {
          key: 'shares',
          label: '分享',
          value: formatNumber(props.performanceData.shares_day1),
          icon: Share,
          color: '#e6a23c',
        },
      ],
    },
    {
      key: 'day3',
      label: '第3天',
      metrics: [
        {
          key: 'views',
          label: '观看',
          value: formatNumber(props.performanceData.views_day3),
          icon: View,
          color: '#409eff',
        },
        {
          key: 'likes',
          label: '点赞',
          value: formatNumber(props.performanceData.likes_day3),
          icon: Star,
          color: '#f56c6c',
        },
        {
          key: 'comments',
          label: '评论',
          value: formatNumber(props.performanceData.comments_day3),
          icon: ChatDotRound,
          color: '#67c23a',
        },
        {
          key: 'shares',
          label: '分享',
          value: formatNumber(props.performanceData.shares_day3),
          icon: Share,
          color: '#e6a23c',
        },
      ],
    },
    {
      key: 'day7',
      label: '第7天',
      metrics: [
        {
          key: 'views',
          label: '观看',
          value: formatNumber(props.performanceData.views_day7),
          icon: View,
          color: '#409eff',
        },
        {
          key: 'likes',
          label: '点赞',
          value: formatNumber(props.performanceData.likes_day7),
          icon: Star,
          color: '#f56c6c',
        },
        {
          key: 'comments',
          label: '评论',
          value: formatNumber(props.performanceData.comments_day7),
          icon: ChatDotRound,
          color: '#67c23a',
        },
        {
          key: 'shares',
          label: '分享',
          value: formatNumber(props.performanceData.shares_day7),
          icon: Share,
          color: '#e6a23c',
        },
      ],
    },
  ]
})

// 方法
const handleClose = () => {
  visible.value = false
}

const handleEdit = () => {
  if (props.performanceData) {
    // 复制数据到编辑表单
    Object.assign(editForm, { ...props.performanceData })
    showEditDialog.value = true
    emit('edit', props.performanceData)
  }
}

const handleEditCancel = () => {
  ElMessageBox.confirm('确定要放弃本次编辑吗？', '确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      showEditDialog.value = false
      // 重置表单
      nextTick(() => {
        editFormRef.value?.resetFields()
      })
    })
    .catch(() => {
      // 用户取消
    })
}

const handleEditSave = async () => {
  try {
    // 验证表单
    await editFormRef.value?.validate()

    editLoading.value = true

    // 模拟保存请求
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 更新时间戳
    editForm.updated_at = new Date().toISOString()

    // 发送保存事件
    emit('save', { ...editForm })

    ElMessage.success('保存成功')
    showEditDialog.value = false
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请检查输入信息')
  } finally {
    editLoading.value = false
  }
}

// 工具方法
const formatNumber = (num?: number | null) => {
  if (num == null) return '-'
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (dateString?: string | null) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

const getPlatformLabel = (platform: string) => {
  const labels: Record<string, string> = {
    tiktok: 'TikTok',
    instagram: 'Instagram',
    youtube: 'YouTube',
  }
  return labels[platform] || platform
}

// 支付信息相关的辅助函数
const formatAmount = (amount: string | null) => {
  if (!amount) return '0.00'
  const num = parseFloat(amount)
  return num.toFixed(2)
}

const getFundSourceLabel = (source: string | null) => {
  if (!source) return '未知'
  const labels: Record<string, string> = {
    company_account: '公司账户',
    project_budget: '项目预算',
    marketing_budget: '营销预算',
    other: '其他',
  }
  return labels[source] || source
}

const getFundSourceTagType = (source: string | null) => {
  if (!source) return 'info'
  const types: Record<string, string> = {
    company_account: 'success',
    project_budget: 'primary',
    marketing_budget: 'warning',
    other: 'info',
  }
  return types[source] || 'info'
}

const viewPaymentScreenshot = () => {
  if (props.performanceData?.payment?.payment_screenshot_filename) {
    // 这里可以实现查看支付截图的逻辑
    // 例如打开一个新窗口或显示图片预览对话框
    ElMessage.info('查看支付截图功能待实现')
  }
}

// 导出组件
defineExpose({
  // 可以暴露一些方法给父组件使用
})
</script>

<style scoped>
/* 复用KolDetailDrawer的完整样式 */
.performance-detail-drawer :deep(.el-drawer) {
  border-radius: 12px 0 0 12px;
  background: #f8fafc;
}

.performance-detail-drawer :deep(.el-drawer__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  margin-bottom: 0;
  border-radius: 12px 0 0 0;
}

.performance-detail-drawer :deep(.el-drawer__body) {
  padding: 0;
  background: #f8fafc;
}

/* 抽屉头部 */
.drawer-header {
  padding: 20px 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.header-info {
  flex: 1;
}

.drawer-title {
  color: white;
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
}

.drawer-subtitle {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 14px;
}

.header-actions .el-button {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-weight: 600;
  border-radius: 8px;
  padding: 12px 24px;
  transition: all 0.3s ease;
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.edit-main-button {
  position: relative;
  overflow: hidden;
}

.edit-main-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.edit-main-button:hover::before {
  left: 100%;
}

.edit-button-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.edit-hint {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin-top: 2px;
}

/* 抽屉内容 */
.drawer-content {
  padding: 0;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 固定操作栏样式 */
.fixed-action-bar {
  position: sticky;
  top: 0;
  z-index: 100;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
}

.action-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 32px;
  min-height: 60px;
}

.action-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.performance-quick-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.performance-avatar {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.performance-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.quick-kol {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1;
}

.quick-status {
  font-size: 12px;
  color: #6b7280;
  line-height: 1;
}

.action-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.action-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
}

.edit-btn-top {
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  padding: 8px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.edit-btn-top:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.edit-btn-top .el-icon {
  margin-right: 4px;
}

/* 内容滚动区域 */
.content-scroll-area {
  flex: 1;
  overflow-y: auto;
  padding: 20px 32px 32px;
}

/* 各个区块的间距 */
.content-scroll-area > div {
  margin-bottom: 20px;
}

.content-scroll-area > div:last-child {
  margin-bottom: 0;
}

/* 卡片通用样式 */
.el-card {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.last-update {
  font-size: 12px;
  color: #9ca3af;
}

/* 合并后的基本信息与详细信息卡片 */
.info-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.info-card {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  background: transparent; /* Ensure background is transparent for the gradient effect */
}

.info-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.info-card :deep(.el-descriptions__label) {
  font-weight: 600;
  color: #374151;
  width: 120px;
  text-align: right;
}

.info-card :deep(.el-descriptions__content) {
  text-align: left;
}

.info-card :deep(.el-descriptions__content a) {
  color: #667eea;
  text-decoration: none;
}

.info-card :deep(.el-descriptions__content a:hover) {
  text-decoration: underline;
}

.no-data {
  color: #9ca3af;
  font-style: italic;
}

/* 数据指标卡片 */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.metric-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

/* 分阶段数据 */
.timeline-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.timeline-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.timeline-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 16px 0;
}

.timeline-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.timeline-metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.metric-name {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.timeline-metric .metric-value {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

/* 数据趋势图卡片 */
.trend-chart-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}
.trend-chart-card :deep(.el-card__header) {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
}
.trend-chart-card :deep(.el-card__body) {
  padding: 20px;
}

/* 操作栏提示样式 */
.action-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: rgba(102, 126, 234, 0.08);
  border-radius: 12px;
  color: #667eea;
  font-size: 12px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all 0.3s ease;
}

.action-hint:hover {
  background: rgba(102, 126, 234, 0.12);
  transform: scale(1.02);
}

.action-hint .el-icon {
  font-size: 14px;
}

.action-hint span {
  font-weight: 500;
}

/* 编辑弹窗样式 */
.edit-dialog :deep(.el-dialog) {
  border-radius: 16px;
  max-height: 90vh;
  overflow: hidden;
}

.edit-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  border-radius: 16px 16px 0 0;
}

.edit-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 20px;
}

.edit-dialog :deep(.el-dialog__close) {
  color: white;
  font-size: 18px;
}

.edit-dialog :deep(.el-dialog__body) {
  padding: 0;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

/* 编辑表单容器 */
.edit-form-container {
  background: #f8fafc;
  padding: 24px;
}

.edit-form {
  background: transparent;
}

/* 表单分组样式 */
.form-section {
  background: white;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.section-title .el-icon {
  color: #667eea;
}

.section-content {
  padding: 24px;
}

/* 表单项样式优化 */
.edit-form :deep(.el-form-item) {
  margin-bottom: 20px;
}

.edit-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.edit-form :deep(.el-input__wrapper) {
  border-radius: 8px;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.edit-form :deep(.el-input__wrapper:hover) {
  border-color: #667eea;
}

.edit-form :deep(.el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.edit-form :deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

.edit-form :deep(.el-input-number .el-input__wrapper) {
  border-radius: 8px;
}

.edit-form :deep(.el-date-editor.el-input) {
  border-radius: 8px;
}

/* 平台选项样式 */
.platform-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 弹窗底部按钮 */
.dialog-footer {
  padding: 20px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f8fafc;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-footer .el-button {
  border-radius: 8px;
  font-weight: 500;
  padding: 12px 24px;
}

/* 响应式优化 */
@media (max-width: 1400px) {
  .performance-detail-drawer {
    --el-drawer-size: 60% !important;
  }
}

@media (max-width: 1200px) {
  .performance-detail-drawer {
    --el-drawer-size: 75% !important;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .timeline-metrics {
    grid-template-columns: repeat(2, 1fr);
  }

  /* 中等屏幕下的顶部区域优化 */
  .info-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .info-item {
    padding: 10px 14px;
  }
}

@media (max-width: 1000px) {
  .performance-detail-drawer {
    --el-drawer-size: 85% !important;
  }

  /* 小屏幕下的顶部区域优化 */
  .header-main {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .header-actions {
    justify-content: center;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
}

@media (max-width: 768px) {
  .performance-detail-drawer {
    --el-drawer-size: 100% !important;
  }

  .content-scroll-area {
    padding: 16px 20px 24px;
  }

  .drawer-header {
    padding: 16px 24px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  /* 移动端顶部区域优化 */
  .perf-header {
    padding: 16px 20px;
  }

  .header-main {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .kol-info {
    justify-content: center;
  }

  .header-actions {
    justify-content: center;
    flex-direction: column;
    gap: 8px;
  }

  .view-post-link,
  .edit-button {
    width: 100%;
    justify-content: center;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .info-item {
    padding: 12px 16px;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .timeline-metrics {
    grid-template-columns: repeat(2, 1fr);
  }

  .edit-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }

  .action-bar-content {
    padding: 12px 20px;
    flex-direction: column;
    gap: 12px;
    min-height: auto;
  }

  .action-left,
  .action-center,
  .action-right {
    flex: none;
    width: 100%;
    justify-content: center;
  }

  .action-center {
    order: -1;
  }

  .edit-btn-top {
    width: 100%;
  }
}

.drawer-header-enhanced {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px 12px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 0 0 0;
  min-height: 80px;
}
.header-left {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  margin-right: 24px;
}
.platform-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
}
.header-center {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.project-row {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 15px;
  color: #6b7280;
}
.project-code {
  font-weight: 500;
}
.platform-name {
  font-size: 15px;
  color: #e0e7ff;
}
.header-right {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  min-width: 180px;
}
.quick-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  font-size: 14px;
  color: #e0e7ff;
  margin-bottom: 4px;
}
.edit-main-button {
  background: #eaf1fb;
  border: 1px solid #e0e7ef;
  color: #6366f1;
  font-weight: 600;
  border-radius: 8px;
  padding: 10px 24px;
  transition: all 0.3s ease;
}
.edit-main-button:hover {
  background: #dbeafe;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.06);
}
/* 简化的顶部区域样式 */
.perf-header {
  padding: 20px 24px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.header-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.kol-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.kol-icon {
  color: #6366f1;
  font-size: 18px;
}

.kol-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.kol-value {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.view-post-link {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 500;
}

.edit-button {
  font-size: 14px;
  padding: 8px 16px;
}

/* 信息网格样式 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.info-item:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.info-item .el-icon {
  color: #6366f1;
  font-size: 16px;
  flex-shrink: 0;
}

.info-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
  min-width: 60px;
}

.info-value {
  font-size: 14px;
  color: #1f2937;
  font-weight: 600;
  flex: 1;
}

.summary-card {
  margin-bottom: 20px;
}
.summary-grid {
  display: flex;
  gap: 32px;
  justify-content: space-between;
}
.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 80px;
}
.summary-value {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}
.summary-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}
.stage-card {
  margin-bottom: 20px;
}

/* 确保分阶段数据卡片有足够的宽度 */
.stage-card :deep(.el-card__body) {
  padding: 24px;
  min-height: 160px;
}

.stage-card :deep(.el-card__header) {
  padding: 16px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
}

/* 防止数据溢出的额外保护 */
.stage-item {
  overflow: hidden;
  word-wrap: break-word;
}

.stage-metrics {
  overflow: visible;
}

.metric-value {
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  line-height: 1.2;
}
.stage-grid {
  display: flex;
  gap: 20px;
  justify-content: space-between;
  align-items: stretch;
  min-height: 120px;
}
.stage-item {
  flex: 1;
  min-width: 0; /* 防止 flex 子项溢出 */
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.stage-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  text-align: center;
  white-space: nowrap;
}
.stage-metrics {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}
.stage-metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 50px;
  flex: 1;
}
.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  word-break: break-all;
}
.metric-label {
  font-size: 11px;
  color: #6b7280;
  text-align: center;
  white-space: nowrap;
}
.trend-card {
  margin-bottom: 20px;
}
.info-card {
  margin-bottom: 20px;
}
.info-card :deep(.el-descriptions__label) {
  font-weight: 600;
  color: #374151;
  width: 120px;
  text-align: right;
}
.info-card :deep(.el-descriptions__content) {
  text-align: left;
}
.info-card :deep(.el-descriptions__content a) {
  color: #667eea;
  text-decoration: none;
}
.info-card :deep(.el-descriptions__content a:hover) {
  text-decoration: underline;
}
/* 分阶段数据响应式优化 */
@media (max-width: 1200px) {
  .stage-grid {
    gap: 16px;
  }
  .stage-metrics {
    gap: 8px;
  }
  .stage-metric {
    min-width: 45px;
  }
  .metric-value {
    font-size: 13px;
  }
  .metric-label {
    font-size: 10px;
  }
}

@media (max-width: 900px) {
  .perf-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 16px 8px 16px;
  }
  .summary-grid {
    flex-direction: column;
    gap: 16px;
  }
  .stage-grid {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  .stage-item {
    min-height: auto;
    padding: 20px;
  }
  .stage-metrics {
    justify-content: space-around;
    gap: 16px;
  }
  .stage-metric {
    min-width: 60px;
    flex: none;
  }
  .metric-value {
    font-size: 16px;
  }
  .metric-label {
    font-size: 12px;
  }
}

@media (max-width: 600px) {
  /* Sub-info 卡片在小屏幕上的样式 */
  .sub-info {
    gap: 12px;
  }

  .sub-info .info-card {
    min-width: 120px;
    padding: 14px;
  }

  .sub-info .info-card:nth-child(3) {
    flex: 0.7;
    min-width: 100px;
  }

  .sub-info .info-value {
    font-size: 15px;
  }

  .stage-metrics {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }
  .stage-metric {
    flex-direction: row;
    gap: 8px;
    min-width: auto;
    width: 100%;
    justify-content: space-between;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
  }
  .metric-label {
    order: -1;
    font-size: 13px;
    font-weight: 500;
  }
  .metric-value {
    font-size: 16px;
    font-weight: 700;
  }
}
.post-link-row {
  margin-top: 8px;
}
.post-link-row .el-link {
  font-size: 15px;
  font-weight: 500;
  color: #6366f1;
  background: #eaf1fb;
  border-radius: 6px;
  padding: 4px 12px;
  transition: background 0.2s;
}
.post-link-row .el-link:hover {
  background: #dbeafe;
  color: #4338ca;
}

/* 极小屏幕响应式设计 */
@media (max-width: 400px) {
  .sub-info {
    flex-direction: column;
    gap: 12px;
  }

  .sub-info .info-card,
  .sub-info .info-card:nth-child(3) {
    flex: none;
    min-width: 100%;
    padding: 12px;
  }

  .sub-info .info-header {
    margin-bottom: 8px;
  }

  .sub-info .info-icon {
    font-size: 16px;
  }

  .sub-info .info-label {
    font-size: 12px;
  }

  .sub-info .info-value {
    font-size: 14px;
  }
}
.platform-img-full {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  display: block;
}

/* 支付信息卡片样式 */
.payment-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.payment-card :deep(.el-card__header) {
  padding: 16px 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 1px solid #e2e8f0;
  color: #374151;
}

.payment-card :deep(.el-card__body) {
  padding: 24px;
}

/* 支付信息描述列表样式 */
.payment-descriptions :deep(.el-descriptions__label) {
  font-weight: 600;
  color: #374151;
  width: 120px;
  text-align: right;
}

.payment-descriptions :deep(.el-descriptions__content) {
  text-align: left;
}

/* 支付金额特殊样式 */
.payment-amount {
  font-size: 18px;
  font-weight: 700;
  color: #059669;
}
</style>
